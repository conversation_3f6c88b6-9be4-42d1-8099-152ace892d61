const API_ROUTE = {
	PROJECT: {
		GET_ALL: "/projects",
		GET_ONE: (projectId: string | number) => `/projects/${projectId}`,
		NEW: "/projects",
		GET_ALL_GROUP: "/projects/groups",
		NEW_GROUP: "/projects/groups",
		UPDATE_GROUP: (groupId: string | number) => `/projects/groups/${groupId}`,
		SOFT_DELETE_GROUP: (groupId: string | number) => `/projects/groups/${groupId}/delete`,
		RECOVER_GROUP: (groupId: string | number) => `/projects/groups/${groupId}/recover`,
		DELETE_GROUP: (groupId: string | number) => `/projects/groups/${groupId}`,
		UPDATE_PROJECT: (projectId: string | number) => `/projects/${projectId}`,
		DELETE_PROJECT: (projectId: string | number) => `/projects/${projectId}`,
	},
	EDUCATION: {
		GET_ALL: "/education",
		GET_ONE: (educationId: string | number) => `/education/${educationId}`,
		NEW: "/education",
		UPDATE: (educationId: string | number) => `/education/${educationId}`,
		SOFT_DELETE: (educationId: string | number) => `/education/${educationId}/delete`,
		RECOVER: (educationId: string | number) => `/education/${educationId}/recover`,
		PERMANENT_DELETE: (educationId: string | number) => `/education/${educationId}`,
	},
	CERTIFICATION: {
		GET_ALL: "/certification",
		GET_ONE: (certId: string | number) => `/certification/${certId}`,
		NEW: "/certification",
		UPDATE: (certId: string | number) => `/certification/${certId}`,
		SOFT_DELETE: (certId: string | number) => `/certification/${certId}/delete`,
		RECOVER: (certId: string | number) => `/certification/${certId}/recover`,
		PERMANENT_DELETE: (certId: string | number) => `/certification/${certId}`,
	},
	EMPLOYMENT: {
		GET_ALL: "/employment",
		GET_ONE: (employmentId: string | number) => `/employment/${employmentId}`,
		NEW: "/employment",
		UPDATE: (employmentId: string | number) => `/employment/${employmentId}`,
		SOFT_DELETE: (employmentId: string | number) => `/employment/${employmentId}/delete`,
		RECOVER: (employmentId: string | number) => `/employment/${employmentId}/recover`,
		PERMANENT_DELETE: (employmentId: string | number) => `/employment/${employmentId}`,
	},
	ACCOUNT: {
		GET_ALL: "/accounts",
		GET_ONE: (accountId: string | number) => `/accounts/${accountId}`,
		SIGN_UP: "/accounts/sign-up",
		SIGN_IN: "/accounts/sign-in",
		RFTK: "/accounts/rftk",
		ACTIVE_STATUS: (accountId: string | number) => `/accounts/${accountId}/active`,
		// UPDATE: (accountId: string | number) => `/accounts/${accountId}`,
		// SOFT_DELETE: (accountId: string | number) => `/accounts/${accountId}/delete`,
		// RECOVER: (accountId: string | number) => `/accounts/${accountId}/recover`,
		// PERMANENT_DELETE: (accountId: string | number) => `/accounts/${accountId}`,
	},
	APP: {
		GET_ALL: "/apps",
		GET_ONE: (appId: string | number) => `/apps/${appId}`,
		NEW: "/apps",
		UPDATE_INFO: (appId: string | number) => `/apps/${appId}`,
		UPDATE_DISPLAY: (appId: string | number) => `/apps/${appId}/display-status`,
		DELETE: (appId: string | number) => `/apps/${appId}`,
	},
};

export default API_ROUTE;
