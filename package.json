{"name": "hieutn-server", "version": "0.0.0", "private": true, "scripts": {"start": "nodemon --env-file=.env app.js", "build": "node --env-file=.env app.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.729.0", "@aws-sdk/s3-request-presigner": "^3.730.0", "bcrypt": "^5.1.1", "body-parser": "^1.20.3", "cookie-parser": "~1.4.4", "cors": "^2.8.5", "debug": "~2.6.9", "express": "~4.16.1", "http-errors": "~1.6.3", "jade": "~1.11.0", "jsonwebtoken": "^9.0.2", "morgan": "~1.9.1", "multer": "^1.4.5-lts.1", "mysql2": "^3.12.0", "nodemon": "^3.1.9", "sharp": "^0.33.5", "socket.io": "^4.8.1"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}