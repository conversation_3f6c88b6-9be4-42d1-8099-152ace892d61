{"name": "portfolio-v4", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"autoprefixer": "^10.4.20", "axios": "^1.7.9", "clsx": "^2.1.1", "dayjs": "^1.11.13", "postcss": "^8.4.49", "quill": "^2.0.3", "react": "^18.3.1", "react-cookie": "^7.2.2", "react-day-picker": "^9.5.0", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hot-toast": "^2.5.1", "react-icons": "^5.4.0", "react-quill": "^2.0.0", "react-router": "^7.1.1", "tailwindcss": "^3.4.17"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}