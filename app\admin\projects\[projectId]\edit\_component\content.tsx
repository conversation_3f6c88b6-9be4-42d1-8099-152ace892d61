"use client";

import Container from "@/components/shared/container/container";
import CustomForm from "@/components/shared/forms/custom-form";
import AdminHeader from "@/components/shared/partials/admin-header";
import API_ROUTE from "@/configs/api";
import ICON_CONFIG from "@/configs/icons";
import { formats, modules } from "@/configs/quill";
import { MAP_MESSAGE } from "@/configs/response-message";
import ROUTE_PATH from "@/configs/route-path";
import { useFetch } from "@/hooks/useFetch";
import { IAPIResponse } from "@/types/global";
import { TUpdateProject, TProjectGroup, TProjectImage, TProjectResponse } from "@/types/project";
import { formatDate } from "@/utils/date";
import {
	Dropdown,
	Button,
	Chip,
	Input,
	Textarea,
	Divider,
	Select,
	SelectItem,
	addToast,
	DateRangePicker,
	RangeValue,
} from "@heroui/react";
import clsx from "clsx";
import Image from "next/image";
import { useState, useEffect, useMemo } from "react";

const ReactQuill = dynamic(() => import("react-quill-new"), { ssr: false });
import { DateValue, parseDate } from "@internationalized/date";
import moment from "moment";
import dynamic from "next/dynamic";

export default function EditProjectComponent({ projectId }: { projectId: string }) {
	const [initArticle, setInitArticle] = useState("");
	const [convertText, setConvertText] = useState<string>("Something...");
	const [projectDetails, setProjectDetails] = useState<TUpdateProject>({
		project_fullname: "",
		project_shortname: "",
		start_date: "",
		end_date: "",
		project_thumbnail: null,
		short_description: "",
		article_body: "",
		group_id: null,
		github_link: "",
		demo_link: "",
		project_images: null,
	});

	const [listProjectGroups, setListProjectGroups] = useState<TProjectGroup[]>([]);
	const [currentThumbnail, setCurrentThumbnail] = useState<string>("");
	const [listCurrentImages, setListCurrentImages] = useState<TProjectImage[]>([]);
	const [listRemoveImages, setListRemoveImages] = useState<string[]>([]);

	/* HANDLE FETCH PROJECT GROUPS */

	const {
		data: fetchProjectGroupsResult,
		error: fetchProjectGroupsError,
		loading: fetchingProjectGroups,
		fetch: fetchProjectGroups,
	} = useFetch<IAPIResponse<TProjectGroup[]>>(API_ROUTE.PROJECT.GET_ALL_GROUP);

	useEffect(() => {
		if (fetchProjectGroupsResult && fetchProjectGroupsResult.results) {
			setListProjectGroups(fetchProjectGroupsResult.results);
		}

		if (fetchProjectGroupsError) {
			const parseError = JSON.parse(fetchProjectGroupsError);

			if (parseError.message) {
				addToast({
					title: "Error",
					description: MAP_MESSAGE[parseError.message],
					color: "danger",
				});
			}
		}
	}, [fetchProjectGroupsResult, fetchProjectGroupsError]);

	/* HANDLE FETCH PROJECT DETAILS */

	const {
		data: fetchProjectDetailResult,
		error: fetchProjectDetailError,
		loading: fetchingProjectDetail,
		fetch: fetchProjectDetail,
	} = useFetch<IAPIResponse<TProjectResponse>>(API_ROUTE.PROJECT.GET_ONE(projectId));

	useEffect(() => {
		if (fetchProjectDetailResult && fetchProjectDetailResult.results) {
			setProjectDetails({
				...fetchProjectDetailResult.results,
				start_date: formatDate(fetchProjectDetailResult.results.start_date, "onlyDateReverse"),
				end_date: formatDate(fetchProjectDetailResult.results.end_date, "onlyDateReverse"),
				project_thumbnail: null,
				project_images: null,
			});

			setCurrentThumbnail(fetchProjectDetailResult.results.project_thumbnail);
			setListCurrentImages(fetchProjectDetailResult.results.project_images);
			setConvertText(fetchProjectDetailResult.results.article_body);
			setInitArticle(fetchProjectDetailResult.results.article_body);
			// setSelectTime({
			// 	from: new Date(fetchProjectDetailResult.results.start_date),
			// 	to: new Date(fetchProjectDetailResult.results.end_date),
			// });
		}
	}, [fetchProjectDetailResult, fetchProjectDetailError]);

	// const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
	// 	e.preventDefault();

	// 	if (!projectId) {
	// 		return;
	// 	}

	// 	const formData = new FormData(e.target as HTMLFormElement);
	// 	formData.append("article_body", convertText);
	// 	formData.append("group_id", projectDetails.group_id?.toString() || "null");
	// 	formData.append("is_change_thumbnail", projectDetails.project_thumbnail ? "true" : "false");
	// 	formData.append("is_change_article", projectDetails.article_body !== initArticle ? "true" : "false");
	// 	formData.append("remove_images", JSON.stringify(listRemoveImages));

	// 	const promiseFn = axios
	// 		.patch(API_ROUTE.PROJECT.UPDATE_PROJECT(projectId), formData)
	// 		.then((response) => response.data)
	// 		.then(() => {
	// 			getProjectDetails(projectId);
	// 		});

	// 	toast.promise(promiseFn, {
	// 		loading: "Updating...",
	// 		success: "Update project successfully",
	// 		error: (error) => error.response.data.message,
	// 	});
	// };

	/* HANDLE PARSE DATE */

	const [datePicked, setDatePicked] = useState<RangeValue<DateValue> | null>({
		start: parseDate(moment().format("YYYY-MM-DD")),
		end: parseDate(moment().add(1, "days").format("YYYY-MM-DD")),
	});

	/* HANDLE REACT QUILL */

	const handleAddRemoveImage = (imageName: string) => {
		if (listRemoveImages.includes(imageName)) {
			setListRemoveImages((prev) => prev.filter((v) => v !== imageName));
		} else {
			setListRemoveImages((prev) => [...prev, imageName]);
		}
	};

	useEffect(() => {
		setProjectDetails((prev) => ({ ...prev, article_body: convertText }));
	}, [convertText]);

	useEffect(() => {
		console.log("projectDetails", projectDetails);
	}, [projectDetails]);

	return (
		<Container
			orientation={"vertical"}
			className={"gap-4"}
		>
			<AdminHeader
				title={"Edit Project Information"}
				backButton={{
					color: "default",
					size: "lg",
					variant: "solid",
					startContent: ICON_CONFIG.BACK,
					text: "Back",
					href: ROUTE_PATH.ADMIN.PROJECT.INDEX,
				}}
				breadcrumbs={["Projects", "Edit"]}
			/>
			<div className={"w-full border border-default-200 bg-white rounded-2xl shadow-lg p-4 flex flex-col gap-4"}>
				<CustomForm
					formId={"editProjectForm"}
					className={"w-full flex flex-col gap-4"}
				>
					<h2 className={"text-2xl font-semibold"}>Project Information</h2>
					<Divider />
					<div className={"grid grid-cols-3 gap-4"}>
						<Input
							label={"Full Project Name"}
							labelPlacement={"outside"}
							type={"text"}
							value={projectDetails.project_fullname}
							name={"project_fullname"}
							placeholder={"Enter project name..."}
							size={"lg"}
							onValueChange={(value) =>
								setProjectDetails((prev) => ({ ...prev, project_fullname: value }))
							}
						/>

						<Input
							label={"Short Project Name"}
							type={"text"}
							size={"lg"}
							value={projectDetails.project_shortname}
							name={"project_shortname"}
							labelPlacement={"outside"}
							placeholder={"Enter short name of project"}
							onValueChange={(e) => setProjectDetails((prev) => ({ ...prev, project_shortname: e }))}
						/>
						<Select
							label={"Select group"}
							labelPlacement={"outside"}
							placeholder={"Select project group"}
							items={listProjectGroups}
							size={"lg"}
							onChange={(e) => setProjectDetails((prev) => ({ ...prev, group_id: e.target.value }))}
						>
							{(item) => <SelectItem key={item.group_id}>{item.group_title}</SelectItem>}
						</Select>
						<div className={"w-full col-span-3"}>
							<Textarea
								label={"Description"}
								labelPlacement={"outside"}
								value={projectDetails.short_description}
								name={"short_description"}
								placeholder={""}
								onValueChange={(e) =>
									setProjectDetails((prev) => ({
										...prev,
										short_description: e,
									}))
								}
							/>
						</div>
						<DateRangePicker
							label={"Start date"}
							labelPlacement={"outside"}
							value={datePicked}
							onChange={setDatePicked}
							aria-label={"Start date"}
						/>
						<Input
							label={"Github"}
							labelPlacement={"outside"}
							placeholder={"Enter Github link"}
							type={"text"}
							value={projectDetails.github_link || ""}
							name={"github_link"}
							onValueChange={(e) =>
								setProjectDetails((prev) => ({
									...prev,
									github_link: e,
								}))
							}
						/>
						<Input
							label={"Demo"}
							labelPlacement={"outside"}
							placeholder={"Enter Demo link"}
							type={"text"}
							value={projectDetails.demo_link || ""}
							name={"demo_link"}
							onValueChange={(e) =>
								setProjectDetails((prev) => ({
									...prev,
									demo_link: e,
								}))
							}
						/>

						<div className={"col-span-3 grid grid-cols-2 gap-4"}>
							<Input
								type={"file"}
								label={"Project Thumbnail"}
								labelPlacement={"outside"}
								placeholder={"Select thumbnail for project"}
								name={"project_thumbnail"}
								onChange={(e) => {
									setProjectDetails((prev) => ({
										...prev,
										project_thumbnail:
											e.target.files && e.target.files.length > 0 ? e.target.files : null,
									}));
								}}
							/>
							<Input
								label={"List Project Images"}
								labelPlacement={"outside"}
								name={"project_images"}
								type={"file"}
								placeholder={"Select thumbnail for project"}
								multiple={true}
								onChange={(e) => {
									setProjectDetails((prev) => ({
										...prev,
										project_images:
											e.target.files && e.target.files.length > 0 ? e.target.files : null,
									}));
								}}
							/>
						</div>
						<div className={"col-span-3 w-full"}>
							<ReactQuill
								modules={modules}
								formats={formats}
								// value={convertText}
								// onChange={setConvertText}
								className={"h-[calc(100%-2.5rem)]"}
							/>
						</div>
					</div>
					<Button
						size={"lg"}
						type={"submit"}
					>
						Submit
					</Button>
				</CustomForm>
				<div className={"w-full shadow-xl rounded-2xl p-4 bg-white"}>
					<div className={"w-full grid grid-cols-3 gap-4"}>
						<div className={clsx("relative border-4 rounded-2xl overflow-hidden border-dark/50")}>
							{/* <Image
								src={currentThumbnail}
								alt={"Current Project thumbnail"}
								className={clsx("shadow-lg rounded-2xl")}
							/> */}
							<div className={"absolute top-2 right-2"}>
								<Chip color={"success"}>Current Thumbnail</Chip>
							</div>
						</div>
						{listCurrentImages.map((image) => (
							<div
								className={clsx("relative border-4 rounded-2xl overflow-hidden", {
									"border-danger": listRemoveImages.includes(image.image_name),
									"border-dark/50": !listRemoveImages.includes(image.image_name),
								})}
							>
								{/* <Image
									src={image.image_url}
									alt={image.image_name}
									className={clsx("shadow-lg rounded-2xl", {
										" blur-sm": listRemoveImages.includes(image.image_name),
									})}
								/> */}
								<Button
									color={"danger"}
									size={"md"}
									className={"absolute top-2 right-2"}
									isIconOnly
									onPress={() => handleAddRemoveImage(image.image_name)}
								>
									{ICON_CONFIG.SOFT_DELETE}
								</Button>
							</div>
						))}
					</div>
				</div>
			</div>
		</Container>
	);
}
